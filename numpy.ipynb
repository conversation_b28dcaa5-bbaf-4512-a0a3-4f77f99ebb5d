{"cells": [{"cell_type": "markdown", "id": "9e486791", "metadata": {}, "source": ["# Numpy"]}, {"cell_type": "markdown", "id": "1a149a8a", "metadata": {}, "source": ["## Create a NumPy ndarray Object"]}, {"cell_type": "code", "execution_count": 1, "id": "8eb0baea", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1 2 3 4 5]\n", "<class 'numpy.ndarray'>\n"]}], "source": ["import numpy as np\n", "\n", "# list\n", "arr = np.array([1, 2, 3, 4, 5])\n", "\n", "print(arr)\n", "\n", "print(type(arr))"]}, {"cell_type": "markdown", "id": "9f35800e", "metadata": {}, "source": ["\n", "- ndarray ย่อมาจาก N-dimensional array หรือ อาร์เรย์ N มิติ"]}, {"cell_type": "code", "execution_count": 60, "id": "5c279a34", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1 2 3 4 5]\n"]}], "source": ["import numpy as np\n", "\n", "# tuple\n", "arr = np.array((1, 2, 3, 4, 5))\n", "\n", "print(arr)"]}, {"cell_type": "markdown", "id": "753b3171", "metadata": {}, "source": ["## NumPy Data Types\n", "\n", "- i - integer\n", "- b - boolean\n", "- u - unsigned integer\n", "- f - float\n", "- c - complex float\n", "- m - <PERSON><PERSON><PERSON>\n", "- M - datetime\n", "- O - object\n", "- S - string\n", "- U - unicode string\n", "- V - fixed chunk of memory for other type ( void )"]}, {"cell_type": "code", "execution_count": 61, "id": "713ca957", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<U6\n"]}], "source": ["import numpy as np\n", "\n", "arr = np.array(['apple', 'banana', 'cherry'])\n", "\n", "print(arr.dtype)"]}, {"cell_type": "code", "execution_count": 64, "id": "43f4872d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[b'1' b'2' b'3' b'4']\n", "|S1\n"]}], "source": ["# Creating Arrays With a Defined Data Type\n", "import numpy as np\n", "\n", "arr = np.array([1, 2, 3, 4], dtype='S')\n", "\n", "print(arr)\n", "print(arr.dtype)"]}, {"cell_type": "code", "execution_count": 63, "id": "0a0a7078", "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "invalid literal for int() with base 10: 'a'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mC<PERSON>\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[63]\u001b[39m\u001b[32m, line 3\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnumpy\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnp\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m3\u001b[39m arr = \u001b[43mnp\u001b[49m\u001b[43m.\u001b[49m\u001b[43marray\u001b[49m\u001b[43m(\u001b[49m\u001b[43m[\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43ma\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43m2\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43m3\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdtype\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43mi\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n", "\u001b[31mValueError\u001b[39m: invalid literal for int() with base 10: 'a'"]}], "source": ["import numpy as np\n", "\n", "arr = np.array(['a', '2', '3'], dtype='i')"]}, {"cell_type": "markdown", "id": "76aa968d", "metadata": {}, "source": ["## Dimensions in Arrays"]}, {"cell_type": "code", "execution_count": 67, "id": "508551cd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["42\n", "0\n"]}], "source": ["import numpy as np\n", "\n", "arr = np.array(42)\n", "\n", "print(arr)\n", "print(arr.ndim)  # Number of dimensions"]}, {"cell_type": "code", "execution_count": 68, "id": "3eda9ae6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1 2 3 4 5]\n", "1\n"]}], "source": ["import numpy as np\n", "\n", "arr = np.array([1, 2, 3, 4, 5])\n", "\n", "print(arr)\n", "print(arr.ndim)  # Number of dimensions"]}, {"cell_type": "code", "execution_count": 69, "id": "2190a724", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[1 2 3]\n", " [4 5 6]]\n", "2\n"]}], "source": ["import numpy as np\n", "\n", "arr = np.array([[1, 2, 3], [4, 5, 6]])\n", "\n", "print(arr)\n", "print(arr.ndim)  # Number of dimensions"]}, {"cell_type": "code", "execution_count": 70, "id": "d305877a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[[1 2 3]\n", "  [4 5 6]]\n", "\n", " [[1 2 3]\n", "  [4 5 6]]]\n", "3\n"]}], "source": ["import numpy as np\n", "\n", "arr = np.array([[[1, 2, 3], [4, 5, 6]], [[1, 2, 3], [4, 5, 6]]])\n", "\n", "print(arr)\n", "print(arr.ndim)  # Number of dimensions"]}, {"cell_type": "code", "execution_count": null, "id": "6efb04ca", "metadata": {}, "outputs": [{"data": {"text/plain": ["np.int64(6)"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["arr = np.array([[[1, 2, 3], [4, 5, 6]], [[6, 5, 4], [3, 2, 1]]])\n", "arr[0,0,0]"]}, {"cell_type": "code", "execution_count": null, "id": "81459082", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Last element from 2nd dim:  [6 5 4]\n"]}], "source": ["print('Last element from 2nd dim: ', arr[1, -1])"]}, {"cell_type": "markdown", "id": "46127baf", "metadata": {}, "source": ["## Slicing arrays\n", "\n", "We pass slice instead of index like this: [start:end].\n", "\n", "We can also define the step, like this: [start:end:step]."]}, {"cell_type": "code", "execution_count": null, "id": "c6f7df19", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[1 2 3 4 5]\n"]}], "source": ["import numpy as np\n", "\n", "arr = np.array([1, 2, 3, 4, 5, 6, 7])\n", "\n", "print(arr[1:5])"]}, {"cell_type": "markdown", "id": "25c1b148", "metadata": {}, "source": ["# NumPy Array Copy vs View"]}, {"cell_type": "code", "execution_count": 77, "id": "b5a0bfc6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[42  2  3  4  5]\n", "[1 2 3 4 5]\n"]}], "source": ["import numpy as np\n", "\n", "arr = np.array([1, 2, 3, 4, 5])\n", "x = arr.copy()\n", "arr[0] = 42\n", "\n", "print(arr)\n", "print(x)"]}, {"cell_type": "code", "execution_count": 78, "id": "980f8c81", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[42  2  3  4  5]\n", "[42  2  3  4  5]\n"]}], "source": ["import numpy as np\n", "\n", "arr = np.array([1, 2, 3, 4, 5])\n", "x = arr.view()\n", "arr[0] = 42\n", "\n", "print(arr)\n", "print(x)"]}, {"cell_type": "code", "execution_count": 79, "id": "141363c8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[31  2  3  4  5]\n", "[31  2  3  4  5]\n"]}], "source": ["import numpy as np\n", "\n", "arr = np.array([1, 2, 3, 4, 5])\n", "x = arr.view()\n", "x[0] = 31\n", "\n", "print(arr)\n", "print(x)"]}, {"cell_type": "code", "execution_count": 80, "id": "3232ead3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["x.base: None\n", "y.base: [1 2 3 4 5]\n", "z.base: None\n", "\n", "Address in memory of arr: 4487686512\n", "Address in memory of x: 4487686608\n", "Address in memory of y: 4487686704\n", "Address in memory of z: 4487686512\n"]}], "source": ["import numpy as np\n", "\n", "arr = np.array([1, 2, 3, 4, 5])\n", "\n", "x = arr.copy()\n", "y = arr.view()\n", "z = arr\n", "\n", "print(\"x.base:\", x.base)\n", "print(\"y.base:\", y.base)\n", "print(\"z.base:\", z.base)\n", "\n", "#print address. in memory of x,y,z\n", "print('\\nAddress in memory of arr:', id(arr))\n", "print('Address in memory of x:', id(x))\n", "print('Address in memory of y:', id(y))\n", "print('Address in memory of z:', id(z))"]}, {"cell_type": "markdown", "id": "c82a5747", "metadata": {}, "source": ["## <PERSON>um<PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 86, "id": "2cd33392", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(2, 4)\n"]}], "source": ["import numpy as np\n", "\n", "arr = np.array([[1, 2, 3, 4], [5, 6, 7, 8]])\n", "\n", "print(arr.shape)"]}, {"cell_type": "markdown", "id": "141ad4db", "metadata": {}, "source": ["The example above returns (2, 4), which means that the array has 2 dimensions, where the first dimension has 2 elements and the second has 4."]}, {"cell_type": "markdown", "id": "ec77f5a4", "metadata": {}, "source": ["## Num<PERSON>y Array Reshaping"]}, {"cell_type": "code", "execution_count": 89, "id": "28d825fd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[ 1  2  3]\n", " [ 4  5  6]\n", " [ 7  8  9]\n", " [10 11 12]]\n"]}], "source": ["import numpy as np\n", "#1-D to 2-D\n", "arr = np.array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12])\n", "\n", "newarr = arr.reshape(4, 3)\n", "\n", "print(newarr)"]}, {"cell_type": "code", "execution_count": 90, "id": "5cf5f647", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[[ 1  2]\n", "  [ 3  4]\n", "  [ 5  6]]\n", "\n", " [[ 7  8]\n", "  [ 9 10]\n", "  [11 12]]]\n"]}], "source": ["import numpy as np\n", "# 1-D to 3-D\n", "arr = np.array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12])\n", "\n", "newarr = arr.reshape(2, 3, 2)\n", "\n", "print(newarr)"]}, {"cell_type": "markdown", "id": "f5559345", "metadata": {}, "source": ["Reference: https://www.w3schools.com/python/numpy/default.asp"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}
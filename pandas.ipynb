{"cells": [{"cell_type": "markdown", "id": "712b5781", "metadata": {}, "source": ["# Pandas\n", "\n", "- Pandas is a Python library.\n", "\n", "- Pandas is used to analyze data.\n", "\n", "- \"Pandas\" has a reference to both \"Panel Data\", and \"Python Data Analysis\" and was created by <PERSON> in 2008."]}, {"cell_type": "markdown", "id": "316b91ea", "metadata": {}, "source": ["## Dictionary"]}, {"cell_type": "code", "execution_count": 1, "id": "86be03e7", "metadata": {}, "outputs": [], "source": ["x = {\n", "    'Name': 'TinTin',\n", "    'Age': 999,\n", "    'Key': 'value'\n", "}"]}, {"cell_type": "code", "execution_count": 2, "id": "3e7f7c5f", "metadata": {}, "outputs": [{"data": {"text/plain": ["'TinTin'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["x['Name']"]}, {"cell_type": "code", "execution_count": null, "id": "5a495d77", "metadata": {}, "outputs": [], "source": ["x = {\n", "    [1, 2, 3], [4, 5, 6]\n", "}\n", "# Error ?"]}, {"cell_type": "code", "execution_count": null, "id": "755c3b51", "metadata": {}, "outputs": [], "source": ["x = {\n", "    'key1':[1, 2, 3],\n", "    'key2':[4, 5, 6]\n", "}\n", "display(x)"]}, {"cell_type": "code", "execution_count": 3, "id": "68bc2473", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'Name': ['<PERSON>', '<PERSON>', '<PERSON>'],\n", " 'Age': [25, 30, 35],\n", " 'City': ['New York', 'London', 'Paris']}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["# column-wise -- dict of list\n", "columnwise_dict = {\n", "    \"Name\": [\"<PERSON>\", \"<PERSON>\", \"<PERSON>\"],\n", "    \"Age\":  [25, 30, 35],\n", "    \"City\": [\"New York\", \"London\", \"Paris\"]\n", "}\n", "\n", "columnwise_dict"]}, {"cell_type": "markdown", "id": "561f12ea", "metadata": {}, "source": ["## DataFrame"]}, {"cell_type": "code", "execution_count": 4, "id": "11857259", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Age</th>\n", "      <th>City</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Alice</td>\n", "      <td>25</td>\n", "      <td>New York</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bob</td>\n", "      <td>30</td>\n", "      <td>London</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON></td>\n", "      <td>35</td>\n", "      <td>Paris</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      Name  Age      City\n", "0    Alice   25  New York\n", "1      Bob   30    London\n", "2  Charlie   35     Paris"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "df_columnwise = pd.DataFrame(columnwise_dict)\n", "df_columnwise.head()"]}, {"cell_type": "code", "execution_count": 14, "id": "fe47dbe8", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Alice'"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["# dict of list\n", "# columnwise_dict['Name']\n", "columnwise_dict['Name'][0]"]}, {"cell_type": "code", "execution_count": 15, "id": "12fe69c2", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Alice'"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# DataFrame column-wise\n", "# df_columnwise['Name']\n", "df_columnwise['Name'][0]"]}, {"cell_type": "code", "execution_count": 8, "id": "ced69d93", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'Name': '<PERSON>', 'Age': 25, 'City': 'New York'},\n", " {'Name': '<PERSON>', 'Age': 30, 'City': 'London'},\n", " {'Name': '<PERSON>', 'Age': 35, 'City': 'Paris'}]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["# row-wise -- list of dict\n", "rowwise_dict = [\n", "    {\"Name\": \"<PERSON>\", \"Age\": 25, \"City\": \"New York\"},\n", "    {\"Name\": \"<PERSON>\", \"Age\": 30, \"City\": \"London\"},\n", "    {\"Name\": \"<PERSON>\", \"Age\": 35, \"City\": \"Paris\"}\n", "]\n", "\n", "rowwise_dict"]}, {"cell_type": "code", "execution_count": 9, "id": "437b83c8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Age</th>\n", "      <th>City</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Alice</td>\n", "      <td>25</td>\n", "      <td>New York</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bob</td>\n", "      <td>30</td>\n", "      <td>London</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON></td>\n", "      <td>35</td>\n", "      <td>Paris</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      Name  Age      City\n", "0    Alice   25  New York\n", "1      Bob   30    London\n", "2  Charlie   35     Paris"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "df_rowwise = pd.DataFrame(rowwise_dict)\n", "df_rowwise.head()"]}, {"cell_type": "code", "execution_count": 18, "id": "e885dec0", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'Name': '<PERSON>', 'Age': 25, 'City': 'New York'}"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["# row-wise -- list of dict\n", "rowwise_dict[0]\n", "# rowwise_dict[0]['Name']"]}, {"cell_type": "code", "execution_count": 21, "id": "e579b93f", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Alice'"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["df_rowwise['Name'][0]"]}, {"cell_type": "code", "execution_count": null, "id": "87bd27a1", "metadata": {}, "outputs": [{"data": {"text/plain": ["Name       <PERSON>\n", "Age           25\n", "City    New York\n", "Name: 0, dtype: object"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["# dataframe row 0 ?\n", "df_rowwise.iloc[0] # index position\n", "df_rowwise.loc[0] # index label"]}, {"cell_type": "code", "execution_count": 24, "id": "96c2dae9", "metadata": {}, "outputs": [], "source": ["df_rowwise.loc[\"test\"] = {\n", "    \"Name\": \"Test\",\n", "    \"Age\": 99,\n", "    \"City\": \"Test City\"\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "f8376d2d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Age</th>\n", "      <th>City</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Alice</td>\n", "      <td>25</td>\n", "      <td>New York</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bob</td>\n", "      <td>30</td>\n", "      <td>London</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON></td>\n", "      <td>35</td>\n", "      <td>Paris</td>\n", "    </tr>\n", "    <tr>\n", "      <th>test</th>\n", "      <td>Test</td>\n", "      <td>99</td>\n", "      <td>Test City</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Name  Age       City\n", "0       Alice   25   New York\n", "1         Bob   30     London\n", "2     Charlie   35      Paris\n", "test     Test   99  Test City"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(df_rowwise)"]}, {"cell_type": "code", "execution_count": null, "id": "f97ea496", "metadata": {}, "outputs": [{"data": {"text/plain": ["Name         Test\n", "Age            99\n", "City    Test City\n", "Name: test, dtype: object"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["df_rowwise.loc[\"test\"]"]}, {"cell_type": "code", "execution_count": 40, "id": "e33faf46", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Alice'"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["df_rowwise['Name'][0] \n", "# df_rowwise.iloc[0, 0] \n", "# df_rowwise.loc[0, 'Name']"]}, {"cell_type": "code", "execution_count": 34, "id": "2283fa0d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>City</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Alice</td>\n", "      <td>New York</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bob</td>\n", "      <td>London</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Name      City\n", "0  Alice  New York\n", "1    Bob    London"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>City</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Alice</td>\n", "      <td>New York</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bob</td>\n", "      <td>London</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Name      City\n", "0  Alice  New York\n", "1    Bob    London"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>City</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Alice</td>\n", "      <td>New York</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bob</td>\n", "      <td>London</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    Name      City\n", "0  Alice  New York\n", "1    Bob    London"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(df_rowwise[['Name','City']][0:2]) # last index is exclusive\n", "\n", "display(df_rowwise.iloc[0:2, [0, 2]]) # index position, last index is exclusive\n", "\n", "display(df_rowwise.loc[0:1, ['Name','City']]) #index label, inclusive"]}, {"cell_type": "markdown", "id": "593881ec", "metadata": {}, "source": ["## Change DataFrame"]}, {"cell_type": "code", "execution_count": 37, "id": "eec7843f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Age</th>\n", "      <th>City</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Alice</td>\n", "      <td>46</td>\n", "      <td>New York</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bob</td>\n", "      <td>28</td>\n", "      <td>Los Angeles</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON></td>\n", "      <td>45</td>\n", "      <td>Chicago</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>David</td>\n", "      <td>18</td>\n", "      <td>Houston</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Emma</td>\n", "      <td>49</td>\n", "      <td>Phoenix</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Frank</td>\n", "      <td>35</td>\n", "      <td>Philadelphia</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Grace</td>\n", "      <td>60</td>\n", "      <td>San Antonio</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td><PERSON></td>\n", "      <td>25</td>\n", "      <td>San Diego</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Ian</td>\n", "      <td>64</td>\n", "      <td>Dallas</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Jack</td>\n", "      <td>25</td>\n", "      <td>San Jose</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      Name  Age          City\n", "0    Alice   46      New York\n", "1      Bob   28   Los Angeles\n", "2  Charlie   45       Chicago\n", "3    David   18       Houston\n", "4     Emma   49       Phoenix\n", "5    Frank   35  Philadelphia\n", "6    Grace   60   San Antonio\n", "7   Hannah   25     San Diego\n", "8      <PERSON>   64        Dallas\n", "9     Jack   25      San Jose"]}, "execution_count": 37, "metadata": {}, "output_type": "execute_result"}], "source": ["import random\n", "import pandas as pd\n", "\n", "names = [\"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\", \"<PERSON>\"]\n", "ages = [random.randint(18, 65) for _ in range(10)]\n", "cities = [\"New York\", \"Los Angeles\", \"Chicago\", \"Houston\", \"Phoenix\", \"Philadelphia\", \"San Antonio\", \"San Diego\", \"Dallas\", \"San Jose\"]\n", "\n", "# Generate random data\n", "data = {\n", "    \"Name\": names,\n", "    \"Age\": ages,\n", "    \"City\": cities\n", "}\n", "\n", "# Create DataFrame\n", "df = pd.DataFrame(data)\n", "df"]}, {"cell_type": "code", "execution_count": 41, "id": "9899cffb", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Age</th>\n", "      <th>City</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>TinTin</td>\n", "      <td>46</td>\n", "      <td>New York</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bob</td>\n", "      <td>28</td>\n", "      <td>Los Angeles</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON></td>\n", "      <td>45</td>\n", "      <td>Chicago</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>David</td>\n", "      <td>18</td>\n", "      <td>Houston</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Emma</td>\n", "      <td>49</td>\n", "      <td>Phoenix</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Frank</td>\n", "      <td>35</td>\n", "      <td>Philadelphia</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Grace</td>\n", "      <td>60</td>\n", "      <td>San Antonio</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td><PERSON></td>\n", "      <td>25</td>\n", "      <td>San Diego</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Ian</td>\n", "      <td>64</td>\n", "      <td>Dallas</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Jack</td>\n", "      <td>25</td>\n", "      <td>San Jose</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      Name  Age          City\n", "0   TinTin   46      New York\n", "1      Bob   28   Los Angeles\n", "2  Charlie   45       Chicago\n", "3    David   18       Houston\n", "4     Emma   49       Phoenix\n", "5    Frank   35  Philadelphia\n", "6    Grace   60   San Antonio\n", "7   Hannah   25     San Diego\n", "8      <PERSON>   64        Dallas\n", "9     Jack   25      San Jose"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["# Change value\n", "\n", "df.loc[0, 'Name'] = 'TinTin'\n", "df"]}, {"cell_type": "code", "execution_count": 42, "id": "5e03905f", "metadata": {}, "outputs": [], "source": ["# df['Name'][0] = '<PERSON>'\n", "# df"]}, {"cell_type": "code", "execution_count": 46, "id": "9ea51ee6", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Age</th>\n", "      <th>City</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>TinTin</td>\n", "      <td>46</td>\n", "      <td>New York</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bob</td>\n", "      <td>28</td>\n", "      <td>Los Angeles</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON></td>\n", "      <td>45</td>\n", "      <td>Chicago</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>David</td>\n", "      <td>18</td>\n", "      <td>Houston</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Emma</td>\n", "      <td>49</td>\n", "      <td>Phoenix</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Frank</td>\n", "      <td>35</td>\n", "      <td>Philadelphia</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Grace</td>\n", "      <td>60</td>\n", "      <td>San Antonio</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td><PERSON></td>\n", "      <td>25</td>\n", "      <td>San Diego</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Ian</td>\n", "      <td>64</td>\n", "      <td>Dallas</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Jack</td>\n", "      <td>25</td>\n", "      <td>San Jose</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>David</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>David</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>David</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>David</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Name  Age          City\n", "0    TinTin   46      New York\n", "1       Bob   28   Los Angeles\n", "2   Charlie   45       Chicago\n", "3     David   18       Houston\n", "4      Emma   49       Phoenix\n", "5     Frank   35  Philadelphia\n", "6     Grace   60   San Antonio\n", "7    Hannah   25     San Diego\n", "8       <PERSON>   64        Dallas\n", "9      Jack   25      San Jose\n", "10    David   40         Tokyo\n", "11    David   40         Tokyo\n", "12    David   40         Tokyo\n", "13    David   40         Tokyo"]}, "execution_count": 46, "metadata": {}, "output_type": "execute_result"}], "source": ["#Adding a new row to df_rowwise\n", "new_row = pd.DataFrame([{'Name':'<PERSON>', 'Age':40, 'City':'Tokyo'}])\n", "df = pd.concat([df, new_row], ignore_index=True)\n", "df"]}, {"cell_type": "code", "execution_count": 47, "id": "e548179a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Age</th>\n", "      <th>City</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>TinTin</td>\n", "      <td>46</td>\n", "      <td>New York</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bob</td>\n", "      <td>28</td>\n", "      <td>Los Angeles</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON></td>\n", "      <td>45</td>\n", "      <td>Chicago</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>David</td>\n", "      <td>18</td>\n", "      <td>Houston</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Emma</td>\n", "      <td>49</td>\n", "      <td>Phoenix</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Frank</td>\n", "      <td>35</td>\n", "      <td>Philadelphia</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Grace</td>\n", "      <td>60</td>\n", "      <td>San Antonio</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td><PERSON></td>\n", "      <td>25</td>\n", "      <td>San Diego</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Ian</td>\n", "      <td>64</td>\n", "      <td>Dallas</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Jack</td>\n", "      <td>25</td>\n", "      <td>San Jose</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>David</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>David</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>David</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>David</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>David2</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Name  Age          City\n", "0    TinTin   46      New York\n", "1       Bob   28   Los Angeles\n", "2   Charlie   45       Chicago\n", "3     David   18       Houston\n", "4      Emma   49       Phoenix\n", "5     Frank   35  Philadelphia\n", "6     Grace   60   San Antonio\n", "7    Hannah   25     San Diego\n", "8       <PERSON>   64        Dallas\n", "9      Jack   25      San Jose\n", "10    David   40         Tokyo\n", "11    David   40         Tokyo\n", "12    David   40         Tokyo\n", "13    David   40         Tokyo\n", "99   David2   40         Tokyo"]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["df.loc[99] = ['David2', 40, 'Tokyo']\n", "df"]}, {"cell_type": "code", "execution_count": 48, "id": "36d37c6c", "metadata": {}, "outputs": [{"data": {"text/plain": ["'add column'"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Age</th>\n", "      <th>City</th>\n", "      <th>Country</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>TinTin</td>\n", "      <td>46</td>\n", "      <td>New York</td>\n", "      <td>test</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bob</td>\n", "      <td>28</td>\n", "      <td>Los Angeles</td>\n", "      <td>test</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON></td>\n", "      <td>45</td>\n", "      <td>Chicago</td>\n", "      <td>test</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>David</td>\n", "      <td>18</td>\n", "      <td>Houston</td>\n", "      <td>test</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Emma</td>\n", "      <td>49</td>\n", "      <td>Phoenix</td>\n", "      <td>test</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Frank</td>\n", "      <td>35</td>\n", "      <td>Philadelphia</td>\n", "      <td>test</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Grace</td>\n", "      <td>60</td>\n", "      <td>San Antonio</td>\n", "      <td>test</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td><PERSON></td>\n", "      <td>25</td>\n", "      <td>San Diego</td>\n", "      <td>test</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Ian</td>\n", "      <td>64</td>\n", "      <td>Dallas</td>\n", "      <td>test</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Jack</td>\n", "      <td>25</td>\n", "      <td>San Jose</td>\n", "      <td>test</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>David</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "      <td>test</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>David</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "      <td>test</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>David</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "      <td>test</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>David</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "      <td>test</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>David2</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "      <td>test</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Name  Age          City Country\n", "0    TinTin   46      New York    test\n", "1       Bob   28   Los Angeles    test\n", "2   <PERSON>   45       Chicago    test\n", "3     David   18       Houston    test\n", "4      Emma   49       Phoenix    test\n", "5     <PERSON>   35  Philadelphia    test\n", "6     Grace   60   San Antonio    test\n", "7    Hannah   25     San Diego    test\n", "8       <PERSON>   64        Dallas    test\n", "9      Jack   25      San Jose    test\n", "10    David   40         Tokyo    test\n", "11    David   40         Tokyo    test\n", "12    David   40         Tokyo    test\n", "13    David   40         Tokyo    test\n", "99   David2   40         Tokyo    test"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Adding a new column to df_rowwise\n", "display(\"add column\")\n", "df['Country'] = \"test\"\n", "display(df)"]}, {"cell_type": "code", "execution_count": 49, "id": "6a60e094", "metadata": {}, "outputs": [{"data": {"text/plain": ["'remove column'"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Age</th>\n", "      <th>City</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>TinTin</td>\n", "      <td>46</td>\n", "      <td>New York</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bob</td>\n", "      <td>28</td>\n", "      <td>Los Angeles</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON></td>\n", "      <td>45</td>\n", "      <td>Chicago</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>David</td>\n", "      <td>18</td>\n", "      <td>Houston</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Emma</td>\n", "      <td>49</td>\n", "      <td>Phoenix</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Frank</td>\n", "      <td>35</td>\n", "      <td>Philadelphia</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Grace</td>\n", "      <td>60</td>\n", "      <td>San Antonio</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td><PERSON></td>\n", "      <td>25</td>\n", "      <td>San Diego</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Ian</td>\n", "      <td>64</td>\n", "      <td>Dallas</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Jack</td>\n", "      <td>25</td>\n", "      <td>San Jose</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>David</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>David</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>David</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>David</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "    </tr>\n", "    <tr>\n", "      <th>99</th>\n", "      <td>David2</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Name  Age          City\n", "0    TinTin   46      New York\n", "1       Bob   28   Los Angeles\n", "2   Charlie   45       Chicago\n", "3     David   18       Houston\n", "4      Emma   49       Phoenix\n", "5     Frank   35  Philadelphia\n", "6     Grace   60   San Antonio\n", "7    Hannah   25     San Diego\n", "8       <PERSON>   64        Dallas\n", "9      Jack   25      San Jose\n", "10    David   40         Tokyo\n", "11    David   40         Tokyo\n", "12    David   40         Tokyo\n", "13    David   40         Tokyo\n", "99   David2   40         Tokyo"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# remove column\n", "display(\"remove column\")\n", "df = df.drop(columns=['Country'])\n", "display(df)"]}, {"cell_type": "code", "execution_count": 51, "id": "7ed6ce64", "metadata": {}, "outputs": [{"data": {"text/plain": ["'remove row'"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Age</th>\n", "      <th>City</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>TinTin</td>\n", "      <td>46</td>\n", "      <td>New York</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bob</td>\n", "      <td>28</td>\n", "      <td>Los Angeles</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON></td>\n", "      <td>45</td>\n", "      <td>Chicago</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>David</td>\n", "      <td>18</td>\n", "      <td>Houston</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Emma</td>\n", "      <td>49</td>\n", "      <td>Phoenix</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Frank</td>\n", "      <td>35</td>\n", "      <td>Philadelphia</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Grace</td>\n", "      <td>60</td>\n", "      <td>San Antonio</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td><PERSON></td>\n", "      <td>25</td>\n", "      <td>San Diego</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Ian</td>\n", "      <td>64</td>\n", "      <td>Dallas</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Jack</td>\n", "      <td>25</td>\n", "      <td>San Jose</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>David</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>David</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>David</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Name  Age          City\n", "0    TinTin   46      New York\n", "1       Bob   28   Los Angeles\n", "2   Charlie   45       Chicago\n", "3     David   18       Houston\n", "4      Emma   49       Phoenix\n", "5     Frank   35  Philadelphia\n", "6     Grace   60   San Antonio\n", "7    Hannah   25     San Diego\n", "8       <PERSON>   64        Dallas\n", "9      Jack   25      San Jose\n", "11    David   40         Tokyo\n", "12    David   40         Tokyo\n", "13    David   40         Tokyo"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# # remove row\n", "display(\"remove row\")\n", "df = df.drop(index=[10])\n", "display(df)"]}, {"cell_type": "code", "execution_count": 52, "id": "90bda57b", "metadata": {}, "outputs": [{"data": {"text/plain": ["'reset index'"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Age</th>\n", "      <th>City</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>TinTin</td>\n", "      <td>46</td>\n", "      <td>New York</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Bob</td>\n", "      <td>28</td>\n", "      <td>Los Angeles</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON></td>\n", "      <td>45</td>\n", "      <td>Chicago</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>David</td>\n", "      <td>18</td>\n", "      <td>Houston</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Emma</td>\n", "      <td>49</td>\n", "      <td>Phoenix</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>Frank</td>\n", "      <td>35</td>\n", "      <td>Philadelphia</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Grace</td>\n", "      <td>60</td>\n", "      <td>San Antonio</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td><PERSON></td>\n", "      <td>25</td>\n", "      <td>San Diego</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Ian</td>\n", "      <td>64</td>\n", "      <td>Dallas</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>Jack</td>\n", "      <td>25</td>\n", "      <td>San Jose</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>David</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>David</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>David</td>\n", "      <td>40</td>\n", "      <td>Tokyo</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Name  Age          City\n", "0    TinTin   46      New York\n", "1       Bob   28   Los Angeles\n", "2   Charlie   45       Chicago\n", "3     David   18       Houston\n", "4      Emma   49       Phoenix\n", "5     Frank   35  Philadelphia\n", "6     Grace   60   San Antonio\n", "7    Hannah   25     San Diego\n", "8       <PERSON>   64        Dallas\n", "9      Jack   25      San Jose\n", "10    David   40         Tokyo\n", "11    David   40         Tokyo\n", "12    David   40         Tokyo"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# # reset index\n", "display(\"reset index\")\n", "df = df.reset_index(drop=True)\n", "display(df)"]}, {"cell_type": "code", "execution_count": 53, "id": "386941f0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Age</th>\n", "      <th>City</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>TinTin</td>\n", "      <td>46</td>\n", "      <td>New York</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON></td>\n", "      <td>45</td>\n", "      <td>Chicago</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Emma</td>\n", "      <td>49</td>\n", "      <td>Phoenix</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>Grace</td>\n", "      <td>60</td>\n", "      <td>San Antonio</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>Ian</td>\n", "      <td>64</td>\n", "      <td>Dallas</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      Name  Age         City\n", "0   TinTin   46     New York\n", "2  Charlie   45      Chicago\n", "4     Emma   49      Phoenix\n", "6    Grace   60  San Antonio\n", "8      <PERSON>   64       Dallas"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["# Filter\n", "df_filtered = df[df['Age'] > 40]\n", "df_filtered"]}, {"cell_type": "code", "execution_count": 54, "id": "1d1ba270", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Name</th>\n", "      <th>Age</th>\n", "      <th>City</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <td><PERSON></td>\n", "      <td>45</td>\n", "      <td>Chicago</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      Name  Age     City\n", "2  Charlie   45  Chicago"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["df_filtered = df[(df['Age'] > 40) & (df['City'] == 'Chicago')]\n", "df_filtered"]}, {"cell_type": "markdown", "id": "589b2e19", "metadata": {}, "source": ["### Dataframe to Dictionary"]}, {"cell_type": "code", "execution_count": 55, "id": "2b70bc42", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>A</th>\n", "      <th>B</th>\n", "      <th>C</th>\n", "      <th>D</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>7</td>\n", "      <td>X</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>5</td>\n", "      <td>9</td>\n", "      <td>Y</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>Z</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   A  B  C  D\n", "0  1  4  7  X\n", "1  2  5  9  Y\n", "2  3  6  9  Z"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Records:\n", " [{'A': 1, 'B': 4, 'C': 7, 'D': 'X'}, {'A': 2, 'B': 5, 'C': 9, 'D': 'Y'}, {'A': 3, 'B': 6, 'C': 9, 'D': 'Z'}]\n", "Columns:\n", " {'A': [1, 2, 3], 'B': [4, 5, 6], 'C': [7, 9, 9], 'D': ['X', 'Y', 'Z']}\n", "Index:\n", " {0: {'A': 1, 'B': 4, 'C': 7, 'D': 'X'}, 1: {'A': 2, 'B': 5, 'C': 9, 'D': 'Y'}, 2: {'A': 3, 'B': 6, 'C': 9, 'D': 'Z'}}\n"]}], "source": ["import pandas as pd\n", "\n", "data = {\n", "    'A': [1, 2, 3],\n", "    'B': [4, 5, 6],\n", "    'C': [7,9,9],\n", "    'D': ['X', 'Y', 'Z']\n", "}\n", "# Create DataFrame\n", "df = pd.DataFrame(data)\n", "display(df)\n", "\n", "dict_records = df.to_dict(orient='records')\n", "dict_columns = df.to_dict(orient='list')\n", "dict_index = df.to_dict(orient='index')\n", "\n", "print(\"Records:\\n\", dict_records)\n", "print(\"Columns:\\n\", dict_columns)\n", "print(\"Index:\\n\", dict_index)"]}, {"cell_type": "code", "execution_count": 57, "id": "c4e8e46e", "metadata": {}, "outputs": [], "source": ["# Save DataFrame to CSV\n", "df.to_csv(\"df.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 58, "id": "eeb0f10b", "metadata": {}, "outputs": [], "source": ["# Load DataFrame from CSV\n", "df = pd.read_csv(\"df.csv\")"]}, {"cell_type": "code", "execution_count": 59, "id": "fb82a796", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>A</th>\n", "      <th>B</th>\n", "      <th>C</th>\n", "      <th>D</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>4</td>\n", "      <td>7</td>\n", "      <td>X</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>5</td>\n", "      <td>9</td>\n", "      <td>Y</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>6</td>\n", "      <td>9</td>\n", "      <td>Z</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   A  B  C  D\n", "0  1  4  7  X\n", "1  2  5  9  Y\n", "2  3  6  9  Z"]}, "metadata": {}, "output_type": "display_data"}], "source": ["display(df)"]}, {"cell_type": "markdown", "id": "34714db8", "metadata": {}, "source": ["### <PERSON><PERSON><PERSON>y to Dataframe"]}, {"cell_type": "code", "execution_count": 60, "id": "e155e436", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>A</th>\n", "      <th>B</th>\n", "      <th>C</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4</td>\n", "      <td>5</td>\n", "      <td>6</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   A  B  C\n", "0  1  2  3\n", "1  4  5  6"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "import numpy as np\n", "\n", "arr = np.array([[1, 2, 3], [4, 5, 6]])\n", "df = pd.DataFrame(arr, columns=['A', 'B', 'C'])\n", "display(df)"]}, {"cell_type": "code", "execution_count": 62, "id": "aa4aa490", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["List sum: 0.03060 seconds\n", "Dict sum: 0.02698 seconds\n", "Pandas DataFrame sum: 0.00235 seconds\n"]}], "source": ["# Compare performance: list vs dict vs pandas DataFrame\n", "import time\n", "import random\n", "import numpy as np\n", "import pandas as pd\n", "\n", "# Generate data\n", "n = 10000000\n", "py_list = [random.randint(0, 100) for _ in range(n)]\n", "np_array = np.array(py_list)\n", "df = pd.DataFrame({'col': py_list})\n", "py_dict = {'col': py_list}\n", "\n", "# Sum operation timing\n", "start = time.time()\n", "sum_list = sum(py_list)\n", "print(f\"List sum: {time.time() - start:.5f} seconds\")\n", "\n", "start = time.time()\n", "sum_dict = sum(py_dict['col'])\n", "print(f\"Dict sum: {time.time() - start:.5f} seconds\")\n", "\n", "start = time.time()\n", "sum_df = df['col'].sum()\n", "print(f\"Pandas DataFrame sum: {time.time() - start:.5f} seconds\")"]}, {"cell_type": "markdown", "id": "19b8b143", "metadata": {}, "source": ["### DataFrame Methods for Working with Data\n", "\n", "https://www.geeksforgeeks.org/python-pandas-dataframe/\n", "\n", "|FUNCTION|DESCRIPTION|\n", "|---|---|\n", "|index()|Method returns index (row labels) of the DataFrame|\n", "|insert()|Method inserts a column into a DataFrame|\n", "|add()|Method returns addition of dataframe and other, element-wise (binary operator add)|\n", "|sub()|Method returns subtraction of dataframe and other element-wise (binary operator sub)|\n", "|mul()|Method returns multiplication of dataframe and other, element-wise (binary operator mul)|\n", "|div()|Method returns floating division of dataframe and other element-wise (binary operator truediv)|\n", "|unique()|Method extracts the unique values in the dataframe|\n", "|nunique()|Method returns count of the unique values in the dataframe|\n", "|value_counts()|Method counts the number of times each unique value occurs within the Series|\n", "|columns()|Method returns the column labels of the DataFrame|\n", "|axes()|Method returns a list representing the axes of the DataFrame|\n", "|isnull()|Method creates a Boolean Series for extracting rows with null values|\n", "|notnull()|Method creates a Boolean Series for extracting rows with non-null values|\n", "|isin()|Method extracts rows from a DataFrame where a column value exists in a predefined collection|\n", "|dtypes()|Method returns a Series with the data type of each column. The result’s index is the original DataFrame’s columns|\n", "|astype()|Method converts the data types in a Series|\n", "|values()|Method returns a Numpy representation of the DataFrame i.e only the values in the DataFrame will be returned, the axes labels will be removed|\n", "|sort_values()|Method sorts a data frame in Ascending or Descending order of passed Column|\n", "|sort_index()|Method sorts the values in a DataFrame based on their index positions or labels instead of their values but sometimes a data frame is made out of two or more data frames and hence later index can be changed using this method|\n", "|loc[]|Method retrieves rows based on index label|\n", "|iloc[]|Method retrieves rows based on index position|\n", "|ix[]|Method retrieves DataFrame rows based on either index label or index position. This method combines the best features of the .loc[] and .iloc[] methods|\n", "|rename()|Method is called on a DataFrame to change the names of the index labels or column names|\n", "|drop()|Method is used to delete rows or columns from a DataFrame|\n", "|pop()|Method is used to delete rows or columns from a DataFrame|\n", "|sample()|Method pulls out a random sample of rows or columns from a DataFrame|\n", "|nsmallest()|Method pulls out the rows with the smallest values in a column|\n", "|nlargest()|Method pulls out the rows with the largest values in a column|\n", "|shape()|Method returns a tuple representing the dimensionality of the DataFrame|\n", "|ndim()|Method returns an ‘int’ representing the number of axes / array dimensions. Returns 1 if Series, otherwise returns 2 if DataFrame|\n", "|dropna()|Method allows the user to analyze and drop Rows/Columns with Null values in different ways|\n", "|fillna()|Method manages and let the user replace NaN values with some value of their own|\n", "|rank()|Values in a Series can be ranked in order with this method|\n", "|query()|Method is an alternate string-based syntax for extracting a subset from a DataFrame|\n", "|copy()|Method creates an independent copy of a pandas object|\n", "|duplicated()|Method creates a Boolean Series and uses it to extract rows that have duplicate values|\n", "|drop_duplicates()|Method is an alternative option to identifying duplicate rows and removing them through filtering|\n", "|set_index()|Method sets the DataFrame index (row labels) using one or more existing columns|\n", "|reset_index()|Method resets index of a Data Frame. This method sets a list of integer ranging from 0 to length of data as index|\n", "|where()|Method is used to check a Data Frame for one or more condition and return the result accordingly. By default, the rows not satisfying the condition are filled with NaN value|"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 5}